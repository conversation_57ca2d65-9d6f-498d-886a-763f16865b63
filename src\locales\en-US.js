export default {
  common: {
    close: 'Close'
  },
  app: {
    title: 'Augment Token Manager',
    appHome: 'App Home',
    pluginHome: 'Plugin Home',
    viewTokens: 'View Tokens',
    bookmarkManager: 'Bookmark Manager',
    outlookManager: 'Outlook Manager',
    switchToLight: 'Switch to Light Mode',
    switchToDark: 'Switch to Dark Mode',
    switchToEnglish: 'Switch to English',
    switchToChinese: 'Switch to Chinese',
    settings: 'Settings'
  },
  bookmarkManager: {
    title: 'Bookmark Manager',
    openDataFolder: 'Open Data Storage Folder',
    addBookmark: 'Add Bookmark',
    editBookmark: 'Edit Bookmark',
    deleteBookmark: 'Delete Bookmark',
    openBookmark: 'Open Bookmark',
    emptyState: 'No bookmarks added yet',
    emptyDescription: 'Click "Add Bookmark" to add your frequently used websites',
    form: {
      name: 'Bookmark Name',
      url: 'URL',
      description: 'Description (Optional)',
      save: 'Save',
      cancel: 'Cancel'
    },
    messages: {
      addSuccess: 'Bookmark added successfully',
      updateSuccess: 'Bookmark updated successfully',
      deleteSuccess: 'Bookmark deleted successfully',
      openFolderFailed: 'Failed to open folder'
    },
    dialog: {
      selectOpenMethod: 'Select Open Method',
      copyToClipboard: 'Copy to Clipboard',
      openInBrowser: 'Open in Browser',
      openInBuiltIn: 'Open in Built-in Browser',
      cancel: 'Cancel'
    }
  },
  outlookManager: {
    title: 'Outlook Email Manager',
    addAccount: 'Add Email Account',
    sessionNotice: 'Account information is only valid for the current session and needs to be re-added after closing the app',
    accountInfo: 'Account Information',
    placeholder: 'Email----Password----Refresh Token----Client ID',
    inputHint: 'Please enter in format: Email----Password----Refresh Token----Client ID',
    addAccountBtn: 'Add Account',
    checkStatus: 'Check Status',
    viewEmails: 'View Emails',
    deleteAccount: 'Delete Account',
    accountList: 'Email Account List',
    emptyState: 'No email accounts added yet',
    emptyDescription: 'Please add an email account to get started',
    status: {
      checking: 'Checking...',
      online: 'Online',
      offline: 'Offline',
      error: 'Error'
    },
    messages: {
      addSuccess: 'Email account added successfully',
      deleteSuccess: 'Email account deleted successfully',
      invalidFormat: 'Account information format is incorrect',
      statusCheckFailed: 'Status check failed'
    }
  },
  databaseConfig: {
    title: 'Database Configuration',
    host: 'Host Address',
    port: 'Port',
    database: 'Database Name',
    username: 'Username',
    password: 'Password',
    sslMode: 'SSL Mode',
    enabled: 'Enable Database Storage',
    testConnection: 'Test Connection',
    saveConfig: 'Save Configuration',
    deleteConfig: 'Delete Configuration',
    cancel: 'Cancel',
    sslModes: {
      disable: 'Disable',
      allow: 'Allow',
      prefer: 'Prefer',
      require: 'Require',
      verifyCA: 'Verify CA',
      verifyFull: 'Verify Full'
    },
    messages: {
      testSuccess: 'Database connection test successful!',
      testFailed: 'Database connection test failed',
      saveSuccess: 'Database configuration saved successfully',
      saveFailed: 'Failed to save configuration',
      deleteSuccess: 'Database configuration deleted',
      deleteFailed: 'Failed to delete configuration',
      loadFailed: 'Failed to load configuration',
      confirmDelete: 'Are you sure you want to delete the database configuration? This will disable database storage functionality.'
    },
    placeholders: {
      host: 'localhost',
      port: '5432',
      database: 'augment_tokens',
      username: 'postgres',
      password: 'Enter password'
    }
  },
  emailViewer: {
    title: 'Email Management',
    folder: 'Folder',
    inbox: 'Inbox',
    junk: 'Junk',
    previousPage: 'Previous',
    nextPage: 'Next',
    reload: 'Reload',
    pageInfo: 'Page {current} / {total}',
    totalEmails: '{count} emails total',
    loading: 'Loading...',
    noEmails: 'No emails',
    from: 'From',
    subject: 'Subject',
    date: 'Date',
    hasAttachment: 'Has Attachment',
    viewDetails: 'View Details',
    messages: {
      loadFailed: 'Failed to load emails'
    }
  },
  tokenList: {
    title: 'Saved Tokens',
    empty: 'No saved tokens yet',
    emptyDescription: 'Close this window and generate your first token on the main page',
    loading: 'Loading tokens...',
    listTitle: 'Token List ({count})',
    refresh: 'Refresh',
    addToken: 'Add Token',
    save: 'Save',
    databaseConfig: 'Database Config'
  },
  tokenCard: {
    banned: 'Banned',
    active: 'Active',
    inactive: 'Inactive',
    expiry: 'Expires',
    balance: 'Balance',
    copyEmailNote: 'Copy Email Note',
    selectEditor: 'Select Editor',
    copyToken: 'Copy Token',
    copyTenantUrl: 'Copy Tenant URL',
    checkAccountStatus: 'Check Account Status',
    openPortal: 'Open Portal',
    editToken: 'Edit Token',
    deleteToken: 'Delete Token',
    canUse: 'Can Use',
    exhausted: 'Usage Exhausted',
    tokenInvalid: 'Token Invalid'
  },
  tokenForm: {
    title: 'Add/Edit Token',
    addTitle: 'Add Token',
    editTitle: 'Edit Token',
    tenantUrl: 'Tenant URL',
    tenantUrlPlaceholder: 'https://example.augmentcode.com/',
    accessToken: 'Access Token',
    accessTokenPlaceholder: 'Please enter access token...',
    portalUrl: 'Portal URL',
    portalUrlPlaceholder: 'https://portal.example.com/',
    portalUrlHelp: 'Used to view account balance and expiration time',
    emailNote: 'Email Note',
    emailNotePlaceholder: 'Please enter email-related notes',
    emailNoteHelp: 'Used to record email information related to this Token',
    optional: 'Optional',
    required: 'Required',
    save: 'Save',
    update: 'Update',
    cancel: 'Cancel'
  },
  tokenGenerator: {
    title: 'Generate Augment Token',
    description: 'Follow these steps to get your Augment access token',
    step1: 'Step 1: Generate Augment Authorization URL',
    step2: 'Step 2: Enter Authorization Code',
    step3: 'Step 3: Augment Access Token',
    step4: 'Step 4: Save Token',
    generateUrl: 'Generate Augment Authorization URL',
    authUrlLabel: 'Authorization URL:',
    authUrlPlaceholder: 'Click the button above to generate authorization URL',
    openAuthUrl: 'Open Authorization URL',
    authCode: 'Authorization Code',
    authCodePlaceholder: 'Paste authorization code JSON here...',
    getToken: 'Get Access Token',
    tenantUrl: 'Tenant URL',
    accessToken: 'Access Token',
    accessTokenLabel: 'Access Token:',
    tenantUrlLabel: 'Tenant URL:',
    portalUrl: 'Portal URL',
    portalUrlPlaceholder: 'Please enter Portal address (optional)',
    emailNote: 'Email Note',
    emailNotePlaceholder: 'Please enter email related note (optional)',
    saveToken: 'Save Token',
    saveAndClose: 'Save and Close',
    copyUrl: 'Copy URL',
    copyTenantUrl: 'Copy Tenant URL',
    copyAccessToken: 'Copy Access Token',
    copy: 'Copy'
  },
  storage: {
    localStorage: 'Local Storage',
    dualStorage: 'Dual Storage',
    databaseStorage: 'Database Storage',
    unsaved: 'Unsaved',
    saved: 'Saved',
    status: 'Storage Status',
    syncData: 'Click to sync data',
    detectDatabase: 'Click to detect database',
    clickToSync: 'Click to perform bidirectional sync',
    clickToDetect: 'Click to detect database connection',
    local: 'Local Storage',
    dual: 'Dual Storage',
    database: 'Database Storage',
    unknown: 'Unknown'
  },
  buttons: {
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    close: 'Close',
    copy: 'Copy',
    refresh: 'Refresh',
    add: 'Add',
    confirm: 'Confirm',
    back: 'Back'
  },
  messages: {
    tokenSaved: 'Token saved successfully',
    tokenAddedToMemory: 'Token added to memory, please save manually',
    tokenUpdatedToMemory: 'Token updated to memory, please save manually',
    tokenUpdated: 'Token updated successfully',
    tokenLoadSuccess: 'Tokens loaded successfully',
    tokenLoadFailed: 'Failed to load tokens',
    tokenSaveFailed: 'Failed to save token',
    tokenUpdateFailed: 'Failed to update token',
    copySuccess: 'URL copied to clipboard!',
    copyFailed: 'Failed to copy URL',
    tenantUrlCopied: 'Tenant URL copied to clipboard!',
    accessTokenCopied: 'Access token copied to clipboard!',
    generateUrlError: 'Failed to generate authorization URL',
    getTokenError: 'Failed to get token',
    syncComplete: 'Bidirectional sync completed',
    syncFailed: 'Sync failed',
    databaseDetected: 'Database connection detected successfully, switched to dual storage mode',
    databaseNotDetected: 'No database connection detected, still in local storage mode',
    openAppHomeFailed: 'Failed to open app home',
    openPluginHomeFailed: 'Failed to open plugin home',
    tokenDeleted: 'Token deleted',
    tokenDeleteFailed: 'Token deletion failed: specified token not found',
    deleteFailed: 'Failed to delete token',
    newTokenSaved: 'New token saved!',
    error: 'Error',
    gettingToken: 'Getting access token...',
    tokenGetSuccess: 'Access token retrieved successfully!',
    portalLinkCopied: 'Portal link copied to clipboard!',
    copyPortalLinkFailed: 'Failed to copy portal link',
    openPortalFailed: 'Failed to open portal',
    openAuthUrlFailed: 'Failed to open authorization URL',
    oauthTitle: 'Augment OAuth Authorization',
    appHomeTitle: 'App Home - Augment Token Manager',
    pluginHomeTitle: 'Plugin Home - Augment Code Auto',
    noTokensToCheck: 'No tokens to check',
    accountStatusCheckComplete: 'Account status check complete ({success}/{total})',
    accountStatusCheckFailed: 'Account status check failed ({failed}/{total})',
    accountStatusCheckPartial: 'Account status partially checked ({success}/{total})',
    accountStatusCheckError: 'Error occurred while checking account status',
    unsavedChangesClose: 'Unsaved changes detected, please save before closing',
    unsavedChangesRefresh: 'Unsaved changes detected, please save before refreshing',
    bidirectionalSyncing: 'Performing bidirectional sync...',
    bidirectionalSyncComplete: 'Bidirectional sync complete',
    syncCompleteButStatusFailed: 'Sync complete, but account status check failed',
    refreshingTokenStatus: 'Refreshing token status and portal information...',
    refreshFailed: 'Refresh failed',
    databaseConfigSaved: 'Database configuration saved, storage functionality updated',
    databaseConfigDeleted: 'Database configuration deleted, switched to local storage only',
    bidirectionalSyncSaveComplete: 'Bidirectional sync save complete',
    saveFailed: 'Save failed',
    updatingToken: 'Updating token...',
    savingToken: 'Saving token...',
    tokenUpdatedToMemory: 'Token updated to memory, please save manually',
    updateFailed: 'Update token failed',
    tokenCopied: 'Token copied to clipboard!',
    copyTokenFailed: 'Failed to copy token',
    copyTenantUrlFailed: 'Failed to copy tenant URL',
    emailNoteCopied: 'Email note copied to clipboard!',
    copyEmailNoteFailed: 'Failed to copy email note',
    editorTokenFileCreated: '{editor} Token file created',
    createEditorTokenFileFailed: 'Failed to create {editor} Token file: {error}',
    openingEditor: 'Opening {editor}...',
    operationFailed: 'Operation failed',
    accountBanned: 'Account banned',
    accountStatusNormal: 'Account status normal',
    statusCheckFailed: 'Status check failed',
    checkComplete: 'Check complete',
    checkFailed: 'Check failed',
    getStorageStatusFailed: 'Failed to get storage status',
    syncFailed: 'Sync failed',
    bidirectionalSyncComplete: 'Bidirectional sync complete',
    databaseDetected: 'Database connection detected successfully, switched to dual storage mode',
    databaseNotDetected: 'No database connection detected, still in local storage mode',
    tokenNotFound: 'Token not found',
    tokenDeleted: 'Token deleted',
    tokenInvalid: 'Token invalid',
    refreshFailedNoTokens: 'Refresh failed: No tokens loaded',
    accountStatus: 'Account Status'
  },
  validation: {
    required: 'This field is required',
    invalidUrl: 'Please enter a valid URL format',
    invalidEmail: 'Please enter a valid email address',
    tenantUrlRequired: 'Tenant URL cannot be empty',
    accessTokenRequired: 'Access token cannot be empty'
  },
  loading: {
    generating: 'Generating...',
    loading: 'Loading...',
    saving: 'Saving...',
    syncing: 'Syncing...',
    refreshing: 'Refreshing...',
    deleting: 'Deleting...'
  },
  dialogs: {
    selectOpenMethod: 'Select Open Method',
    copyLink: 'Copy Link',
    openExternal: 'Open External',
    openInternal: 'Open Internal',
    dontOpen: "Don't Open",
    cancel: 'Cancel',
    confirmDelete: 'Confirm Delete',
    confirmDeleteMessage: 'Are you sure you want to delete this Token? This action cannot be undone.',
    delete: 'Delete',
    appHomeTitle: 'App Home - Select Open Method',
    pluginHomeTitle: 'Plugin Home - Select Open Method',
    authUrlTitle: 'Authorization URL Open Method Selection Dialog'
  }
}
