export default {
  common: {
    close: '关闭'
  },
  app: {
    title: 'Augment Token Manager',
    appHome: 'App主页',
    pluginHome: '插件主页',
    viewTokens: '查看Token',
    bookmarkManager: '书签管理',
    outlookManager: '邮箱管理',
    switchToLight: '切换到白天模式',
    switchToDark: '切换到夜间模式',
    switchToEnglish: '切换到英文',
    switchToChinese: '切换到中文',
    settings: '设置'
  },
  bookmarkManager: {
    title: '书签管理',
    openDataFolder: '打开数据存储文件夹',
    addBookmark: '添加书签',
    editBookmark: '编辑书签',
    deleteBookmark: '删除书签',
    openBookmark: '打开书签',
    emptyState: '还没有添加书签',
    emptyDescription: '点击"添加书签"来添加你常用的网站',
    form: {
      name: '书签名称',
      url: '网址',
      description: '描述（可选）',
      save: '保存',
      cancel: '取消'
    },
    messages: {
      addSuccess: '书签添加成功',
      updateSuccess: '书签更新成功',
      deleteSuccess: '书签删除成功',
      openFolderFailed: '打开文件夹失败'
    },
    dialog: {
      selectOpenMethod: '选择打开方式',
      copyToClipboard: '复制到剪贴板',
      openInBrowser: '在浏览器中打开',
      openInBuiltIn: '内置浏览器打开',
      cancel: '取消'
    }
  },
  outlookManager: {
    title: 'Outlook 邮箱管理',
    addAccount: '添加邮箱账户',
    sessionNotice: '账户信息仅在当前会话中有效，关闭应用后需要重新添加',
    accountInfo: '账户信息',
    placeholder: '邮箱地址----密码----Refresh Token----Client ID',
    inputHint: '请按格式输入：邮箱地址----密码----Refresh Token----Client ID',
    addAccountBtn: '添加账户',
    checkStatus: '检查状态',
    viewEmails: '查看邮件',
    deleteAccount: '删除账户',
    accountList: '邮箱账户列表',
    emptyState: '还没有添加邮箱账户',
    emptyDescription: '请先添加邮箱账户来开始使用',
    status: {
      checking: '检查中...',
      online: '在线',
      offline: '离线',
      error: '错误'
    },
    messages: {
      addSuccess: '邮箱账户添加成功',
      deleteSuccess: '邮箱账户删除成功',
      invalidFormat: '账户信息格式不正确',
      statusCheckFailed: '状态检查失败'
    }
  },
  databaseConfig: {
    title: '数据库配置',
    host: '主机地址',
    port: '端口',
    database: '数据库名',
    username: '用户名',
    password: '密码',
    sslMode: 'SSL模式',
    enabled: '启用数据库存储',
    testConnection: '测试连接',
    saveConfig: '保存配置',
    deleteConfig: '删除配置',
    cancel: '取消',
    sslModes: {
      disable: '禁用',
      allow: '允许',
      prefer: '首选',
      require: '必需',
      verifyCA: '验证CA',
      verifyFull: '完全验证'
    },
    messages: {
      testSuccess: '数据库连接测试成功！',
      testFailed: '数据库连接测试失败',
      saveSuccess: '数据库配置保存成功',
      saveFailed: '保存配置失败',
      deleteSuccess: '数据库配置已删除',
      deleteFailed: '删除配置失败',
      loadFailed: '加载配置失败',
      confirmDelete: '确定要删除数据库配置吗？这将禁用数据库存储功能。'
    },
    placeholders: {
      host: 'localhost',
      port: '5432',
      database: 'augment_tokens',
      username: 'postgres',
      password: '输入密码'
    }
  },
  emailViewer: {
    title: '邮件管理',
    folder: '文件夹',
    inbox: '收件箱',
    junk: '垃圾邮件',
    previousPage: '上一页',
    nextPage: '下一页',
    reload: '重新加载',
    pageInfo: '第 {current} 页 / 共 {total} 页',
    totalEmails: '共 {count} 封邮件',
    loading: '加载中...',
    noEmails: '没有邮件',
    from: '发件人',
    subject: '主题',
    date: '日期',
    hasAttachment: '有附件',
    viewDetails: '查看详情',
    messages: {
      loadFailed: '加载邮件失败'
    }
  },
  tokenList: {
    title: '已保存Token',
    empty: '还没有保存的Token',
    emptyDescription: '关闭此窗口，在主页面生成你的第一个Token',
    loading: '正在加载Token...',
    listTitle: 'Token列表 ({count})',
    refresh: '刷新',
    addToken: '添加Token',
    save: '保存',
    databaseConfig: '数据库配置'
  },
  tokenCard: {
    banned: '已封禁',
    active: '正常',
    inactive: '失效',
    expiry: '过期',
    balance: '剩余',
    copyEmailNote: '复制邮箱备注',
    selectEditor: '选择编辑器',
    copyToken: '复制Token',
    copyTenantUrl: '复制租户URL',
    checkAccountStatus: '检测账号状态',
    openPortal: '打开Portal',
    editToken: '编辑Token',
    deleteToken: '删除Token',
    canUse: '还能使用',
    exhausted: '使用次数耗尽',
    tokenInvalid: 'Token失效'
  },
  tokenForm: {
    title: '添加/编辑Token',
    addTitle: '添加Token',
    editTitle: '编辑Token',
    tenantUrl: '租户URL',
    tenantUrlPlaceholder: 'https://example.augmentcode.com/',
    accessToken: '访问令牌',
    accessTokenPlaceholder: '请输入访问令牌...',
    portalUrl: 'Portal URL',
    portalUrlPlaceholder: 'https://portal.example.com/',
    portalUrlHelp: '用于查看账户余额和过期时间',
    emailNote: '邮箱备注',
    emailNotePlaceholder: '请输入邮箱相关备注',
    emailNoteHelp: '用于记录与此Token相关的邮箱信息',
    optional: '可选',
    required: '必填项',
    save: '保存',
    update: '更新',
    cancel: '取消'
  },
  tokenGenerator: {
    title: '生成Augment Token',
    description: '按照以下步骤获取你的Augment访问令牌',
    step1: '步骤 1: 生成Augment授权URL',
    step2: '步骤 2: 输入授权码',
    step3: '步骤 3: Augment访问令牌',
    step4: '步骤 4: 保存Token',
    generateUrl: '生成Augment授权URL',
    authUrlLabel: '授权URL:',
    authUrlPlaceholder: '点击上方按钮生成授权URL',
    openAuthUrl: '打开授权URL',
    authCode: '授权码',
    authCodePlaceholder: '在此粘贴授权码JSON...',
    getToken: '获取访问令牌',
    tenantUrl: '租户URL',
    accessToken: '访问令牌',
    accessTokenLabel: '访问令牌:',
    tenantUrlLabel: '租户URL:',
    portalUrl: 'Portal URL',
    portalUrlPlaceholder: '请输入 Portal 地址（可选）',
    emailNote: '邮箱备注',
    emailNotePlaceholder: '请输入邮箱相关备注（可选）',
    saveToken: '保存Token',
    saveAndClose: '保存并关闭',
    copyUrl: '复制URL',
    copyTenantUrl: '复制租户URL',
    copyAccessToken: '复制访问令牌',
    copy: '复制'
  },
  storage: {
    localStorage: '本地存储',
    dualStorage: '双向存储',
    databaseStorage: '数据库存储',
    unsaved: '未保存',
    saved: '已保存',
    status: '存储状态',
    syncData: '点击同步数据',
    detectDatabase: '点击检测数据库',
    clickToSync: '点击执行双向同步',
    clickToDetect: '点击检测数据库连接',
    local: '本地存储',
    dual: '双重存储',
    database: '数据库存储',
    unknown: '未知'
  },
  buttons: {
    save: '保存',
    cancel: '取消',
    delete: '删除',
    edit: '编辑',
    close: '关闭',
    copy: '复制',
    refresh: '刷新',
    add: '添加',
    confirm: '确认',
    back: '返回'
  },
  messages: {
    tokenSaved: 'Token保存成功',
    tokenAddedToMemory: 'Token已添加到内存，请手动保存',
    tokenUpdatedToMemory: 'Token已更新到内存，请手动保存',
    tokenUpdated: 'Token更新成功',
    tokenLoadSuccess: 'Token加载成功',
    tokenLoadFailed: '加载Token失败',
    tokenSaveFailed: '保存Token失败',
    tokenUpdateFailed: '更新Token失败',
    copySuccess: 'URL已复制到剪贴板!',
    copyFailed: '复制URL失败',
    tenantUrlCopied: '租户URL已复制到剪贴板!',
    accessTokenCopied: '访问令牌已复制到剪贴板!',
    generateUrlError: '生成授权URL失败',
    getTokenError: '获取Token失败',
    syncComplete: '双向同步完成',
    syncFailed: '同步失败',
    databaseDetected: '数据库连接检测成功，已切换到双重存储模式',
    databaseNotDetected: '未检测到数据库连接，仍为本地存储模式',
    openAppHomeFailed: '打开应用主页失败',
    openPluginHomeFailed: '打开插件主页失败',
    tokenDeleted: 'Token已删除',
    tokenDeleteFailed: 'Token删除失败：未找到指定token',
    deleteFailed: '删除Token失败',
    newTokenSaved: '新Token已保存!',
    error: '错误',
    gettingToken: '正在获取访问令牌...',
    tokenGetSuccess: '访问令牌获取成功!',
    portalLinkCopied: 'Portal链接已复制到剪贴板!',
    copyPortalLinkFailed: '复制Portal链接失败',
    openPortalFailed: '打开Portal失败',
    openAuthUrlFailed: '打开授权URL失败',
    oauthTitle: 'Augment OAuth 授权',
    appHomeTitle: 'App主页 - Augment Token Manager',
    pluginHomeTitle: '插件主页 - Augment Code Auto',
    noTokensToCheck: '没有可检查的 Token',
    accountStatusCheckComplete: '账号状态检查完成 ({success}/{total})',
    accountStatusCheckFailed: '账号状态检查失败 ({failed}/{total})',
    accountStatusCheckPartial: '账号状态部分检查成功 ({success}/{total})',
    accountStatusCheckError: '检查账号状态时发生错误',
    unsavedChangesClose: '检测到未保存的更改，请先保存后再关闭',
    unsavedChangesRefresh: '检测到未保存的更改，请先保存后再刷新',
    bidirectionalSyncing: '正在执行双向同步...',
    bidirectionalSyncComplete: '双向同步完成',
    syncCompleteButStatusFailed: '同步完成，但账号状态检查失败',
    refreshingTokenStatus: '正在刷新 Token 状态和 Portal 信息...',
    refreshFailed: '刷新失败',
    databaseConfigSaved: '数据库配置已保存，存储功能已更新',
    databaseConfigDeleted: '数据库配置已删除，已切换到仅本地存储',
    bidirectionalSyncSaveComplete: '双向同步保存完成',
    saveFailed: '保存失败',
    updatingToken: '正在更新Token...',
    savingToken: '正在保存Token...',
    tokenUpdatedToMemory: 'Token已更新到内存，请手动保存',
    updateFailed: '更新Token失败',
    tokenCopied: 'Token已复制到剪贴板!',
    copyTokenFailed: '复制Token失败',
    copyTenantUrlFailed: '复制租户URL失败',
    emailNoteCopied: '邮箱备注已复制到剪贴板!',
    copyEmailNoteFailed: '复制邮箱备注失败',
    editorTokenFileCreated: '{editor} Token 文件已创建',
    createEditorTokenFileFailed: '创建 {editor} Token 文件失败: {error}',
    openingEditor: '正在打开 {editor}...',
    operationFailed: '操作失败',
    accountBanned: '账号已封禁',
    accountStatusNormal: '账号状态正常',
    statusCheckFailed: '状态检测失败',
    checkComplete: '检测完成',
    checkFailed: '检测失败',
    getStorageStatusFailed: '获取存储状态失败',
    syncFailed: '同步失败',
    bidirectionalSyncComplete: '双向同步完成',
    databaseDetected: '数据库连接检测成功，已切换到双重存储模式',
    databaseNotDetected: '未检测到数据库连接，仍为本地存储模式',
    tokenNotFound: 'Token不存在',
    tokenDeleted: 'Token已删除',
    tokenInvalid: 'Token失效',
    refreshFailedNoTokens: '刷新失败：没有加载到任何Token',
    accountStatus: '账号状态'
  },
  validation: {
    required: '此字段为必填项',
    invalidUrl: '请输入有效的URL格式',
    invalidEmail: '请输入有效的邮箱地址',
    tenantUrlRequired: '租户URL不能为空',
    accessTokenRequired: '访问令牌不能为空'
  },
  loading: {
    generating: '生成中...',
    loading: '加载中...',
    saving: '保存中...',
    syncing: '同步中...',
    refreshing: '刷新中...',
    deleting: '删除中...'
  },
  dialogs: {
    selectOpenMethod: '选择打开方式',
    copyLink: '复制链接',
    openExternal: '外部打开',
    openInternal: '内置打开',
    dontOpen: '不打开',
    cancel: '取消',
    confirmDelete: '确认删除',
    confirmDeleteMessage: '确定要删除这个Token吗？此操作无法撤销。',
    delete: '删除',
    appHomeTitle: 'App主页 - 选择打开方式',
    pluginHomeTitle: '插件主页 - 选择打开方式',
    authUrlTitle: '授权URL打开方式选择对话框'
  }
}
